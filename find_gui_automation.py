import os
import re

def find_gui_automation_files():
    """Find Python files that contain both tkinter and pyautogui imports."""
    
    search_dirs = [
        "C:\\Users\\<USER>\\OneDrive\\Documents",
        "C:\\Users\\<USER>\\Documents", 
        "C:\\Users\\<USER>\\Desktop",
        "C:\\Users\\<USER>\\Downloads"
    ]
    
    found_files = []
    
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            print(f"Searching in: {search_dir}")
            for root, dirs, files in os.walk(search_dir):
                # Skip certain directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'venv', 'env']]
                
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                            
                            # Check for both tkinter and pyautogui
                            has_tkinter = any(keyword in content.lower() for keyword in ['import tkinter', 'from tkinter', 'tkinter.'])
                            has_pyautogui = any(keyword in content.lower() for keyword in ['import pyautogui', 'pyautogui.'])
                            
                            # Look for GUI-related terms
                            gui_terms = ['button', 'window', 'frame', 'label', 'entry', 'click', 'screenshot']
                            gui_count = sum(1 for term in gui_terms if term.lower() in content.lower())
                            
                            # Look for automation-specific terms
                            automation_terms = ['sequence', 'step', 'object', 'region', 'pixel', 'image']
                            automation_count = sum(1 for term in automation_terms if term.lower() in content.lower())
                            
                            if has_tkinter and has_pyautogui and gui_count >= 3 and automation_count >= 2:
                                found_files.append({
                                    'path': file_path,
                                    'size': len(content),
                                    'gui_terms': gui_count,
                                    'automation_terms': automation_count
                                })
                                
                        except Exception as e:
                            pass  # Skip files we can't read
    
    # Sort by relevance (size and term counts)
    found_files.sort(key=lambda x: (x['automation_terms'], x['gui_terms'], x['size']), reverse=True)
    
    print(f"\nFound {len(found_files)} potential automation tool files:")
    print("=" * 60)
    
    for i, file_info in enumerate(found_files):
        print(f"{i+1}. {file_info['path']}")
        print(f"   Size: {file_info['size']} characters")
        print(f"   GUI terms: {file_info['gui_terms']}, Automation terms: {file_info['automation_terms']}")
        print()
    
    return found_files

if __name__ == "__main__":
    find_gui_automation_files()
