# Windows Sleep Timer Application

A simple Python application with a graphical user interface that allows you to put your Windows PC to sleep after a specified amount of time.

## Features

- Set a timer in seconds, minutes, or hours
- Visual countdown display
- Ability to cancel the timer
- Clean and simple user interface

## Requirements

- Windows operating system
- Python 3.6 or higher
- tkinter (usually comes with Python installation)

## How to Use

1. Run the application by executing `sleep_timer.py`
2. Enter the desired time value in the input field
3. Select the time unit (seconds, minutes, or hours) from the dropdown menu
4. Click "Start Timer" to begin the countdown
5. The application will display the remaining time
6. When the timer reaches zero, your PC will be put to sleep
7. You can cancel the timer at any time by clicking the "Cancel" button

## Running the Application

```
python sleep_timer.py
```

## Notes

- The application uses the Windows API to put the PC to sleep, so it will only work on Windows operating systems
- Make sure to save any unsaved work before starting the timer, as the sleep mode will suspend all running applications