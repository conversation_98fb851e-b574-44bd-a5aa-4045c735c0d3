import os
import subprocess

def search_for_automation_content():
    """Search for files containing specific automation tool content."""
    
    # Specific phrases that would be in your automation tool
    search_phrases = [
        "Object-Step-Sequence Model",
        "Step Creator",
        "Object Creation", 
        "Region Objects",
        "Image Objects",
        "Pixel Objects",
        "Main Menu",
        "Save Sequence As",
        "Run Sequence"
    ]
    
    # Search in the entire user directory
    base_path = "C:\\Users\\<USER>\nSearching for: '{phrase}'")
        try:
            # Use findstr to search for the phrase in Python files
            cmd = f'findstr /s /i /m "{phrase}" "{base_path}\\*.py"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.stdout.strip():
                files = result.stdout.strip().split('\n')
                for file_path in files:
                    if file_path.strip():
                        print(f"  Found in: {file_path.strip()}")
            else:
                print(f"  No files found containing '{phrase}'")
                
        except subprocess.TimeoutExpired:
            print(f"  Search for '{phrase}' timed out")
        except Exception as e:
            print(f"  Error searching for '{phrase}': {e}")

if __name__ == "__main__":
    search_for_automation_content()
