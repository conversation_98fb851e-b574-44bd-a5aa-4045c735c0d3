"""
Manual Search Guide for Your Desktop Automation Tool

Based on the search results, your automation tool is likely a large Python file 
(around 89,000+ characters) that contains these key features:

KEY IDENTIFIERS TO LOOK FOR:
1. File size: Around 89,709 characters (large file)
2. Contains: "Step Creator" 
3. Contains: "Object Creation"
4. Contains: p<PERSON>utogui imports
5. Contains: tkinter GUI elements
6. Contains: screenshot functionality
7. Contains: mouse/keyboard automation

LIKELY LOCATIONS:
- C:\Users\<USER>\OneDrive\Documents\GitHub\Useful-tools\
- C:\Users\<USER>\OneDrive\Documents\GitHub\ (any subdirectory)
- C:\Users\<USER>\Documents\
- C:\Users\<USER>\Desktop\

SEARCH STEPS:
1. Open File Explorer
2. Navigate to C:\Users\<USER>\OneDrive\Documents\GitHub\
3. Use the search box in File Explorer
4. Search for: *.py
5. Look for files with names like:
   - "Automation maker.py"
   - "automation*.py" 
   - "*maker*.py"
   - Files with "region", "step", "object" in the name

6. Check file sizes - look for larger Python files (80KB+)
7. Open promising files and search for these text strings:
   - "Object-Step-Sequence"
   - "Step Creator" 
   - "Object Creation"
   - "Region Objects"
   - "Main Menu"

ALTERNATIVE APPROACH:
If you remember any unique text or comments from your program, 
search for those specific phrases in File Explorer.
"""

print(__doc__)

# You can also run this to get a directory listing
import os

def list_github_dirs():
    github_path = r"C:\Users\<USER>\OneDrive\Documents\GitHub"
    if os.path.exists(github_path):
        print(f"\nDirectories in {github_path}:")
        try:
            for item in os.listdir(github_path):
                item_path = os.path.join(github_path, item)
                if os.path.isdir(item_path):
                    print(f"  📁 {item}")
        except Exception as e:
            print(f"Error listing directories: {e}")
    else:
        print(f"GitHub directory not found at {github_path}")

if __name__ == "__main__":
    list_github_dirs()
