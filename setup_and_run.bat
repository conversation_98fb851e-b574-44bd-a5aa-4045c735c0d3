@echo off
echo PowerPoint Automation Bot - Setup and Run Script
echo =============================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

echo Python is installed. Checking and installing required packages...

:: Create and activate virtual environment (optional)
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
)

:: Activate virtual environment
call venv\Scripts\activate.bat

:: Install required packages
echo Installing required packages...
pip install pyautogui keyboard pillow cairosvg

:: Ensure images directory exists
if not exist images (
    echo Creating images directory...
    mkdir images
)

:: Convert SVG assets to PNG if needed
if exist convert_svg_to_png.py (
    echo Converting SVG assets to PNG...
    python convert_svg_to_png.py
)

echo.
echo Setup complete! You can now run the PowerPoint Automation Bot.
echo.
echo Instructions:
echo 1. Make sure your presentation is open and visible
echo 2. Run the automation script
echo 3. Press '#' to start/stop the automation
echo 4. Move mouse to upper-left corner to force-stop
echo.

set /p choice=Do you want to run the bot now? (Y/N): 

if /i "%choice%"=="Y" (
    echo Starting PowerPoint Automation Bot...
    python powerpoint_automation.py
) else (
    echo You can run the bot later with: python powerpoint_automation.py
)

pause