import os
import datetime
from pathlib import Path

def check_transcription_status(voice_recordings_dir, target_date):
    """Check which audio files have corresponding transcription files.
    
    Args:
        voice_recordings_dir: Path to the voice recordings directory
        target_date: Date to check files for (datetime.date object)
    
    Returns:
        Dictionary with results of the analysis
    """
    # Ensure the directory exists
    if not os.path.exists(voice_recordings_dir):
        print(f"Error: Directory '{voice_recordings_dir}' does not exist.")
        return None
    
    # Get the target date as a string for comparison
    target_date_str = target_date.strftime('%Y-%m-%d')
    
    # Lists to track files
    audio_files = []
    transcription_files = []
    segment_files = []
    
    # Audio file extensions to look for
    audio_extensions = ('.mp3', '.wav', '.m4a', '.flac', '.ogg')
    
    # Walk through the directory
    for root, _, files in os.walk(voice_recordings_dir):
        for file in files:
            file_path = os.path.join(root, file)
            file_mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
            file_mod_date = file_mod_time.date()
            
            # Check if the file was modified on the target date
            if file_mod_date == target_date:
                # Check file type
                if file.lower().endswith(audio_extensions):
                    audio_files.append({
                        'path': file_path,
                        'name': file,
                        'modified': file_mod_time
                    })
                elif file.lower().endswith('.txt'):
                    if '_segments' in file.lower():
                        segment_files.append({
                            'path': file_path,
                            'name': file,
                            'modified': file_mod_time
                        })
                    else:
                        transcription_files.append({
                            'path': file_path,
                            'name': file,
                            'modified': file_mod_time
                        })
    
    # Analyze which audio files have transcriptions
    results = {
        'date_analyzed': target_date_str,
        'total_audio_files': len(audio_files),
        'total_transcription_files': len(transcription_files),
        'total_segment_files': len(segment_files),
        'audio_files': audio_files,
        'transcribed_files': [],
        'untranscribed_files': []
    }
    
    # For each audio file, check if there's a corresponding transcription
    for audio in audio_files:
        audio_name = os.path.splitext(audio['name'])[0]
        
        # Look for matching transcription files
        found_transcription = False
        for trans in transcription_files:
            trans_name = os.path.splitext(trans['name'])[0]
            # Check if the transcription filename contains the audio filename
            # (allowing for additional text like date stamps)
            if audio_name in trans_name or trans_name in audio_name:
                results['transcribed_files'].append({
                    'audio': audio,
                    'transcription': trans
                })
                found_transcription = True
                break
        
        # If no transcription found, check for segment files as an alternative
        if not found_transcription:
            found_segments = False
            for seg in segment_files:
                seg_name = os.path.splitext(seg['name'])[0]
                if audio_name in seg_name or seg_name in audio_name:
                    results['transcribed_files'].append({
                        'audio': audio,
                        'segments': seg
                    })
                    found_segments = True
                    break
            
            # If neither transcription nor segments found
            if not found_segments:
                results['untranscribed_files'].append(audio)
    
    return results

def print_results(results):
    """Print the results in a readable format."""
    if not results:
        return
    
    print(f"\n===== TRANSCRIPTION STATUS FOR {results['date_analyzed']} =====")
    print(f"Total audio files: {results['total_audio_files']}")
    print(f"Total transcription files: {results['total_transcription_files']}")
    print(f"Total segment files: {results['total_segment_files']}")
    print(f"Successfully transcribed: {len(results['transcribed_files'])}")
    print(f"Not transcribed: {len(results['untranscribed_files'])}")
    
    if results['untranscribed_files']:
        print("\nAUDIO FILES WITHOUT TRANSCRIPTIONS:")
        for i, audio in enumerate(results['untranscribed_files'], 1):
            print(f"{i}. {audio['name']} (Modified: {audio['modified'].strftime('%H:%M:%S')})")
    
    if results['transcribed_files']:
        print("\nSUCCESSFULLY TRANSCRIBED FILES:")
        for i, item in enumerate(results['transcribed_files'], 1):
            audio = item['audio']
            if 'transcription' in item:
                trans = item['transcription']
                print(f"{i}. {audio['name']} → {trans['name']} (Modified: {trans['modified'].strftime('%H:%M:%S')})")
            elif 'segments' in item:
                seg = item['segments']
                print(f"{i}. {audio['name']} → {seg['name']} (Modified: {seg['modified'].strftime('%H:%M:%S')})")

def main():
    # Directory to check
    voice_recordings_dir = r"G:\My Drive\Voice Recordings"
    
    # Target date (14th of current month)
    target_date = datetime.date(datetime.date.today().year, datetime.date.today().month, 14)
    
    print(f"Checking transcription status for files modified on {target_date.strftime('%Y-%m-%d')}")
    print(f"Directory: {voice_recordings_dir}")
    
    # Run the check
    results = check_transcription_status(voice_recordings_dir, target_date)
    
    # Print results
    if results:
        print_results(results)
    else:
        print("No results to display.")

if __name__ == "__main__":
    main()