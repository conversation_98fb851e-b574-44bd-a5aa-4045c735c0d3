#!/usr/bin/env python3
# Convert SVG images to PNG format for use with PyAutoGUI

import os
import sys
from pathlib import Path

try:
    from cairosvg import svg2png
except ImportError:
    print("Error: cairosvg library not found.")
    print("Please install it with: pip install cairosvg")
    sys.exit(1)

def convert_svg_to_png(svg_path, png_path=None, scale=1.0):
    """Convert an SVG file to PNG format."""
    if png_path is None:
        # Replace .svg extension with .png
        png_path = str(svg_path).replace('.svg', '.png')
    
    try:
        # Convert SVG to PNG
        svg2png(url=str(svg_path), write_to=png_path, scale=scale)
        print(f"Converted: {svg_path} -> {png_path}")
        return True
    except Exception as e:
        print(f"Error converting {svg_path}: {e}")
        return False

def main():
    # Get the directory containing the script
    script_dir = Path(__file__).parent
    images_dir = script_dir / 'images'
    
    # Check if images directory exists
    if not images_dir.exists():
        print(f"Error: Images directory not found at {images_dir}")
        print("Creating images directory...")
        images_dir.mkdir(exist_ok=True)
    
    # Find all SVG files in the images directory
    svg_files = list(images_dir.glob('*.svg'))
    
    if not svg_files:
        print(f"No SVG files found in {images_dir}")
        sys.exit(1)
    
    print(f"Found {len(svg_files)} SVG files to convert:")
    for svg_file in svg_files:
        print(f"  - {svg_file.name}")
    
    # Convert each SVG file to PNG
    success_count = 0
    for svg_file in svg_files:
        # Create PNG path in the images directory
        png_path = images_dir / svg_file.name.replace('.svg', '.png')
        
        # Convert with 2x scale for better recognition
        if convert_svg_to_png(svg_file, png_path, scale=2.0):
            success_count += 1
    
    print(f"\nConversion complete: {success_count}/{len(svg_files)} files converted successfully.")
    print("PNG files are now ready for use with PyAutoGUI.")

if __name__ == "__main__":
    main()