#!/usr/bin/env python3
# Test script for PowerPoint Automation Bot image detection

import os
import sys
import time
from pathlib import Path

try:
    import pyautogui
    from PIL import Image
except ImportError as e:
    print(f"Error: Required library not found - {e}")
    print("Please install required libraries with: pip install pyautogui pillow")
    sys.exit(1)

def test_image_detection():
    """Test if PyAutoGUI can detect the image assets."""
    print("\n=== PowerPoint Automation Bot - Image Detection Test ===\n")
    
    # Get the directory containing the script
    script_dir = Path(__file__).parent
    images_dir = script_dir / 'images'
    
    # Check if images directory exists
    if not images_dir.exists():
        print(f"Error: Images directory not found at {images_dir}")
        sys.exit(1)
    
    # Define image paths
    image_paths = {
        "arrow_forward": "images/arrow_forward_active.png",
        "green_template": "images/green_template.png",
        "close_button": "images/close_button.png",
        "show_button": "images/show_button.png"
    }
    
    # Check if PNG files exist
    missing_pngs = []
    for key, path in image_paths.items():
        if not os.path.exists(path):
            missing_pngs.append(path)
    
    if missing_pngs:
        print("The following PNG files are missing:")
        for path in missing_pngs:
            print(f"  - {path}")
        print("\nWould you like to convert SVG files to PNG? (y/n)")
        choice = input("> ").strip().lower()
        
        if choice == 'y':
            try:
                print("\nAttempting to convert SVG files to PNG...")
                # Check if convert_svg_to_png.py exists
                if os.path.exists("convert_svg_to_png.py"):
                    print("Running convert_svg_to_png.py...")
                    os.system("python convert_svg_to_png.py")
                else:
                    print("Error: convert_svg_to_png.py not found.")
                    sys.exit(1)
            except Exception as e:
                print(f"Error converting SVG files: {e}")
                sys.exit(1)
        else:
            print("\nPlease create the PNG files manually before running this test.")
            sys.exit(1)
    
    # Test image detection
    print("\nTesting image detection...")
    print("This will attempt to locate the images on your screen.")
    print("Please ensure that the target application is visible.")
    print("Press Enter to continue or Ctrl+C to cancel.")
    input()
    
    # Set confidence threshold
    confidence = 0.7
    
    # Test each image
    results = {}
    for key, path in image_paths.items():
        print(f"\nLooking for {key} ({path})...")
        try:
            # Check if file exists
            if not os.path.exists(path):
                print(f"  Error: File not found - {path}")
                results[key] = False
                continue
                
            # Try to locate the image on screen
            location = pyautogui.locateOnScreen(path, confidence=confidence)
            
            if location:
                print(f"  ✓ Found at {location}")
                results[key] = True
            else:
                print(f"  ✗ Not found on screen")
                results[key] = False
                
        except Exception as e:
            print(f"  ✗ Error: {e}")
            results[key] = False
    
    # Print summary
    print("\n=== Detection Summary ===")
    success_count = sum(1 for result in results.values() if result)
    print(f"Found {success_count}/{len(image_paths)} images on screen.")
    
    for key, result in results.items():
        status = "✓ Found" if result else "✗ Not found"
        print(f"{status}: {key} ({image_paths[key]})")
    
    if success_count == 0:
        print("\nNo images were detected. Possible reasons:")
        print("  1. The target application is not visible")
        print("  2. The images don't match the actual UI elements")
        print("  3. The confidence threshold is too high")
        print("\nSuggestions:")
        print("  - Take new screenshots of the actual UI elements")
        print("  - Try lowering the confidence threshold")
        print("  - Make sure the target application is visible")
    elif success_count < len(image_paths):
        print("\nSome images were not detected. Suggestions:")
        print("  - Take new screenshots of the missing UI elements")
        print("  - Try lowering the confidence threshold")
    else:
        print("\nAll images were detected successfully!")
        print("The PowerPoint Automation Bot should work correctly.")

if __name__ == "__main__":
    try:
        test_image_detection()
    except KeyboardInterrupt:
        print("\nTest cancelled by user.")
        sys.exit(0)