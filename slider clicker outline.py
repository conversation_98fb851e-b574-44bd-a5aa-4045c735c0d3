# 1. Import necessary libraries
import pyautogui
import keyboard
import threading
import time

# --- Image Asset Preloading ---
# It's recommended to load all image assets once at the start.
# Ensure you have the following PNG files in the same directory as the script.
try:
    arrow_forward_img = 'arrow_forward_active.png'
    green_template_img = 'green_template.png'
    close_button_img = 'close_button.png'
    show_button_img = 'show_button.png'
    # The dotted line images are for reference and not used in the grid scan logic.
except FileNotFoundError as e:
    print(f"Error: Image file not found - {e}. Please ensure all image assets are in the script's directory.")
    exit()


# 2. Global State Variable
is_running = False

# 3. Start/Stop Hotkey Function
def toggle_automation():
    """Toggles the running state of the automation."""
    global is_running
    is_running = not is_running
    print(f"Automation {'STARTED' if is_running else 'STOPPED'}")

# 4. Main Automation Logic Function
def automation_loop():
    """The main loop for the automation task, executed in a separate thread."""
    while True:
        if is_running:
            try:
                # Priority 1: Check for and click the "Close" button.
                close_location = pyautogui.locateCenterOnScreen(close_button_img, confidence=0.8)
                if close_location:
                    pyautogui.click(close_location)
                    time.sleep(1) # Wait for the pop-up to disappear
                    continue # Restart the loop to re-evaluate priorities

                # Priority 2: Check for and click the "Arrow Forward" button.
                arrow_location = pyautogui.locateCenterOnScreen(arrow_forward_img, confidence=0.8)
                if arrow_location:
                    pyautogui.click(arrow_location)
                    time.sleep(2) # Wait for the next slide to load
                    continue

                # Priority 3: Find and click all "Green Template" items.
                green_locations = list(pyautogui.locateAllOnScreen(green_template_img, confidence=0.8))
                if green_locations:
                    for loc in green_locations:
                        pyautogui.click(pyautogui.center(loc))
                        time.sleep(0.5) # Brief pause after each click
                        # It's good practice to check for a close button immediately after a click
                        if pyautogui.locateOnScreen(close_button_img, confidence=0.8):
                            break # Exit the green item loop to handle the close button
                    continue

                # Priority 4: Handle "Show and Click" slides.
                show_location = pyautogui.locateCenterOnScreen(show_button_img, confidence=0.8)
                if show_location:
                    pyautogui.click(show_location)
                    time.sleep(1) # Wait for dotted lines to appear

                    # Define the area to scan (e.g., the whole screen or a specific portion)
                    screen_width, screen_height = pyautogui.size()
                    scan_region = (0, 0, screen_width, screen_height) # Example: full screen

                    # Execute the grid scan
                    for x in range(scan_region[0], scan_region[2], 50): # Step of 50px
                        for y in range(scan_region[1], scan_region[3], 50):
                            if not is_running: break # Allow stopping mid-scan
                            pyautogui.click(x, y)
                            time.sleep(0.2)
                            # Check for a "Close" button after each click in the grid
                            if pyautogui.locateOnScreen(close_button_img, confidence=0.8):
                                break
                        if not is_running or pyautogui.locateOnScreen(close_button_img, confidence=0.8):
                            break
                    continue

            except pyautogui.PyAutoGUIException as e:
                print(f"An error occurred with PyAutoGUI: {e}")
            
            # A small delay to prevent high CPU usage
            time.sleep(0.5)
        else:
            # If not running, sleep longer to reduce resource usage
            time.sleep(1)


# 5. Main Execution Block
if __name__ == "__main__":
    # Set up the global hotkey
    keyboard.add_hotkey('#', toggle_automation)
    print("Program initialized. Press '#' to start or stop the automation.")

    # Start the automation loop in a daemon thread
    automation_thread = threading.Thread(target=automation_loop, daemon=True)
    automation_thread.start()

    # Keep the main thread alive to listen for the hotkey
    keyboard.wait()