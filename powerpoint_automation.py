#!/usr/bin/env python3
# PowerPoint Automation Bot
# A robust script to automate navigation and interaction within presentations

import os
import sys
import time
import json
import logging
import threading
from datetime import datetime
from pathlib import Path

try:
    import pyautogui
    import keyboard
    from PIL import Image, ImageGrab
except ImportError as e:
    print(f"Error: Required library not found - {e}")
    print("Please install required libraries with: pip install pyautogui keyboard pillow")
    sys.exit(1)

# Configure PyAutoGUI safety features
pyautogui.PAUSE = 0.1  # Add a small pause between PyAutoGUI commands
pyautogui.FAILSAFE = True  # Move mouse to upper-left corner to abort

# Set up logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)
log_file = log_dir / f"powerpoint_automation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("PowerPointAutomation")

# Default configuration
DEFAULT_CONFIG = {
    "hotkey": "#",
    "confidence_threshold": 0.8,
    "grid_step_size": 50,
    "delays": {
        "after_close": 1.0,
        "after_arrow": 2.0,
        "after_green": 0.5,
        "after_show": 1.0,
        "grid_click": 0.2,
        "loop_active": 0.5,
        "loop_inactive": 1.0
    },
    "image_paths": {
        "arrow_forward": "images/arrow_forward_active.png",
        "green_template": "images/green_template.png",
        "close_button": "images/close_button.png",
        "show_button": "images/show_button.png"
    },
    "scan_region": None  # None means full screen
}

# Global state variables
class State:
    running = False
    config = DEFAULT_CONFIG
    images = {}
    stats = {
        "close_clicks": 0,
        "arrow_clicks": 0,
        "green_clicks": 0,
        "show_clicks": 0,
        "grid_clicks": 0,
        "errors": 0,
        "start_time": None
    }

# --- Helper Functions ---

def load_config(config_path="config.json"):
    """Load configuration from a JSON file if it exists, otherwise use defaults."""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                user_config = json.load(f)
                # Merge with defaults to ensure all required fields exist
                merged_config = DEFAULT_CONFIG.copy()
                
                # Handle nested dictionaries properly
                for key, value in user_config.items():
                    if isinstance(value, dict) and key in merged_config and isinstance(merged_config[key], dict):
                        merged_config[key].update(value)
                    else:
                        merged_config[key] = value
                        
                logger.info(f"Loaded configuration from {config_path}")
                return merged_config
        else:
            # Create a default config file for user to modify
            with open(config_path, 'w') as f:
                json.dump(DEFAULT_CONFIG, f, indent=4)
            logger.info(f"Created default configuration file at {config_path}")
            return DEFAULT_CONFIG
    except Exception as e:
        logger.error(f"Error loading configuration: {e}. Using defaults.")
        return DEFAULT_CONFIG

def load_images():
    """Load all image assets and store them for faster access."""
    images = {}
    for key, path in State.config["image_paths"].items():
        try:
            if os.path.exists(path):
                # Store the path for PyAutoGUI to use
                images[key] = path
                logger.info(f"Loaded image asset: {key} from {path}")
            else:
                logger.error(f"Image file not found: {path}")
                print(f"Error: Image file not found - {path}")
                print(f"Please ensure all image assets are in the correct directory.")
                print(f"Current working directory: {os.getcwd()}")
                sys.exit(1)
        except Exception as e:
            logger.error(f"Error loading image {path}: {e}")
            print(f"Error loading image {path}: {e}")
            sys.exit(1)
    return images

def take_screenshot(filename=None):
    """Take a screenshot for debugging purposes."""
    if filename is None:
        filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    
    screenshot_dir = Path("screenshots")
    screenshot_dir.mkdir(exist_ok=True)
    filepath = screenshot_dir / filename
    
    try:
        screenshot = ImageGrab.grab()
        screenshot.save(filepath)
        logger.info(f"Saved screenshot to {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Failed to take screenshot: {e}")
        return None

def print_stats():
    """Print automation statistics."""
    if State.stats["start_time"]:
        duration = time.time() - State.stats["start_time"]
        minutes, seconds = divmod(duration, 60)
        hours, minutes = divmod(minutes, 60)
        duration_str = f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
    else:
        duration_str = "N/A"
    
    stats_msg = f"""
=== Automation Statistics ===
Running time: {duration_str}
Close buttons clicked: {State.stats['close_clicks']}
Arrow buttons clicked: {State.stats['arrow_clicks']}
Green templates clicked: {State.stats['green_clicks']}
Show buttons clicked: {State.stats['show_clicks']}
Grid clicks performed: {State.stats['grid_clicks']}
Errors encountered: {State.stats['errors']}
==========================="""
    
    print(stats_msg)
    logger.info("\n" + stats_msg)

# --- Core Automation Functions ---

def toggle_automation():
    """Toggle the running state of the automation."""
    State.running = not State.running
    
    if State.running:
        logger.info("Automation STARTED")
        print("\n🟢 Automation STARTED")
        State.stats["start_time"] = time.time()
    else:
        logger.info("Automation STOPPED")
        print("\n🔴 Automation STOPPED")
        print_stats()

def find_and_click(image_key, confidence=None, click=True, increment_stat=None):
    """Find an image on screen and optionally click it."""
    if confidence is None:
        confidence = State.config["confidence_threshold"]
    
    image_path = State.images[image_key]
    
    try:
        location = pyautogui.locateCenterOnScreen(image_path, confidence=confidence)
        if location:
            logger.debug(f"Found {image_key} at {location}")
            if click:
                pyautogui.click(location)
                logger.info(f"Clicked {image_key} at {location}")
                if increment_stat:
                    State.stats[increment_stat] += 1
            return location
        return None
    except Exception as e:
        logger.error(f"Error finding {image_key}: {e}")
        State.stats["errors"] += 1
        return None

def find_all_and_click(image_key, confidence=None, increment_stat=None):
    """Find all instances of an image on screen and click them."""
    if confidence is None:
        confidence = State.config["confidence_threshold"]
    
    image_path = State.images[image_key]
    clicked = 0
    
    try:
        locations = list(pyautogui.locateAllOnScreen(image_path, confidence=confidence))
        for loc in locations:
            center = pyautogui.center(loc)
            pyautogui.click(center)
            logger.info(f"Clicked {image_key} at {center}")
            clicked += 1
            
            # Brief pause after each click
            time.sleep(State.config["delays"]["after_green"])
            
            # Check for close button after each click
            if find_and_click("close_button", increment_stat="close_clicks"):
                break
        
        if clicked > 0 and increment_stat:
            State.stats[increment_stat] += clicked
        
        return clicked
    except Exception as e:
        logger.error(f"Error finding all {image_key}: {e}")
        State.stats["errors"] += 1
        return 0

def perform_grid_scan():
    """Perform a grid scan of the screen, clicking at regular intervals."""
    step_size = State.config["grid_step_size"]
    
    # Define scan region
    if State.config["scan_region"]:
        region = State.config["scan_region"]
    else:
        screen_width, screen_height = pyautogui.size()
        region = (0, 0, screen_width, screen_height)
    
    logger.info(f"Starting grid scan with step size {step_size}px in region {region}")
    
    clicks = 0
    for x in range(region[0], region[2], step_size):
        for y in range(region[1], region[3], step_size):
            # Check if automation was stopped
            if not State.running:
                logger.info("Grid scan interrupted - automation stopped")
                return clicks
            
            # Click at the current position
            pyautogui.click(x, y)
            clicks += 1
            logger.debug(f"Grid clicked at ({x}, {y})")
            
            # Brief pause after each click
            time.sleep(State.config["delays"]["grid_click"])
            
            # Check for close button after each click
            if find_and_click("close_button", increment_stat="close_clicks"):
                logger.info("Grid scan interrupted - close button found")
                return clicks
    
    logger.info(f"Grid scan completed with {clicks} clicks")
    State.stats["grid_clicks"] += clicks
    return clicks

def automation_loop():
    """Main automation loop that runs in a separate thread."""
    logger.info("Automation loop started")
    
    while True:
        if State.running:
            try:
                # Priority 1: Check for and click the "Close" button
                if find_and_click("close_button", increment_stat="close_clicks"):
                    time.sleep(State.config["delays"]["after_close"])
                    continue
                
                # Priority 2: Check for and click the "Arrow Forward" button
                if find_and_click("arrow_forward", increment_stat="arrow_clicks"):
                    time.sleep(State.config["delays"]["after_arrow"])
                    continue
                
                # Priority 3: Find and click all "Green Template" items
                if find_all_and_click("green_template", increment_stat="green_clicks") > 0:
                    continue
                
                # Priority 4: Handle "Show and Click" slides
                if find_and_click("show_button", increment_stat="show_clicks"):
                    time.sleep(State.config["delays"]["after_show"])
                    perform_grid_scan()
                    continue
                
                # If no action was taken, add a small delay to prevent high CPU usage
                time.sleep(State.config["delays"]["loop_active"])
            
            except Exception as e:
                logger.error(f"Error in automation loop: {e}")
                State.stats["errors"] += 1
                # Take a screenshot when an error occurs for debugging
                take_screenshot(f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
                time.sleep(1)  # Longer delay after an error
        else:
            # If not running, sleep longer to reduce resource usage
            time.sleep(State.config["delays"]["loop_inactive"])

# --- Main Execution ---

def main():
    """Main entry point for the application."""
    print("\n=== PowerPoint Automation Bot ===\n")
    logger.info("Application started")
    
    # Load configuration
    State.config = load_config()
    
    # Load image assets
    State.images = load_images()
    
    # Set up the global hotkey
    hotkey = State.config["hotkey"]
    keyboard.add_hotkey(hotkey, toggle_automation)
    
    print(f"Program initialized and ready!")
    print(f"Press '{hotkey}' to start or stop the automation.")
    print(f"Move mouse to upper-left corner of screen to force-stop (PyAutoGUI failsafe).")
    logger.info(f"Hotkey '{hotkey}' registered for toggling automation")
    
    # Start the automation loop in a daemon thread
    automation_thread = threading.Thread(target=automation_loop, daemon=True)
    automation_thread.start()
    logger.info("Automation thread started")
    
    try:
        # Keep the main thread alive to listen for the hotkey
        keyboard.wait()
    except KeyboardInterrupt:
        logger.info("Application terminated by user (KeyboardInterrupt)")
        print("\nApplication terminated by user.")
        print_stats()

if __name__ == "__main__":
    main()