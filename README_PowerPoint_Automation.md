# PowerPoint Automation Bot

A robust Python script that automates navigation and interaction within PowerPoint-like presentations by visually identifying and clicking on specific elements in a prioritized order. The script uses image recognition to find and interact with UI elements, making it useful for automating repetitive presentation tasks.

## Features

- **Global Hotkey Control**: Start and stop automation with a configurable hotkey (default: `#`)
- **Prioritized Action System**: Handles UI elements in a specific order of importance
- **Grid Scanning**: Systematically clicks through interactive elements that may not have distinct visual markers
- **Error Handling**: Robust error recovery with logging and screenshot capture
- **Configurable Settings**: Easily adjust timing, confidence thresholds, and other parameters
- **Statistics Tracking**: Monitors clicks, actions, and runtime

## Requirements

- Python 3.6+
- Required libraries: `pyautogui`, `keyboard`, `pillow`

## Installation

1. Ensure you have Python installed
2. Install required dependencies:
   ```
   pip install pyautogui keyboard pillow
   ```
3. Place the script in a directory of your choice
4. Add the required image assets to the same directory (see below)

## Required Image Assets

The script requires the following image files to be present in the `images` directory:

- `images/arrow_forward_active.png`: The active "next slide" button
- `images/green_template.png`: Clickable green elements in the presentation
- `images/close_button.png`: The close button that appears in pop-ups
- `images/show_button.png`: The button that reveals interactive elements

These images should be screenshots of the actual UI elements you want to interact with. The script uses these images to locate and click on matching elements on the screen. SVG versions of these assets are also provided in the `images` directory and can be converted to PNG using the included `convert_svg_to_png.py` script.

## Usage

1. Run the script:
   ```
   python powerpoint_automation.py
   ```
2. Press the configured hotkey (default: `#`) to start/stop the automation
3. Move the mouse to the upper-left corner of the screen to force-stop (PyAutoGUI failsafe)

## Configuration

The script creates a `config.json` file on first run with default settings. You can modify this file to customize the behavior:

```json
{
    "hotkey": "#",
    "confidence_threshold": 0.8,
    "grid_step_size": 50,
    "delays": {
        "after_close": 1.0,
        "after_arrow": 2.0,
        "after_green": 0.5,
        "after_show": 1.0,
        "grid_click": 0.2,
        "loop_active": 0.5,
        "loop_inactive": 1.0
    },
    "image_paths": {
        "arrow_forward": "arrow_forward_active.png",
        "green_template": "green_template.png",
        "close_button": "close_button.png",
        "show_button": "show_button.png"
    },
    "scan_region": null
}
```

### Configuration Options

- `hotkey`: The key to press to start/stop automation
- `confidence_threshold`: How closely an image must match (0.0-1.0)
- `grid_step_size`: Pixel distance between clicks in grid scan mode
- `delays`: Timing adjustments for various actions
- `image_paths`: Locations of the image assets
- `scan_region`: Optional region to limit grid scanning (format: [x, y, width, height])

## How It Works

The script follows a prioritized approach to automation:

1. **Close Button Detection**: Highest priority - clicks any close buttons that appear
2. **Arrow Forward**: Clicks the next slide button when available
3. **Green Templates**: Finds and clicks all green interactive elements
4. **Show Button & Grid Scan**: Clicks the show button and performs a grid scan to interact with all elements

## Logging

The script creates detailed logs in the `logs` directory and captures screenshots on errors in the `screenshots` directory for troubleshooting.

## Customization

To adapt this script for different presentations or applications:

1. Replace the image assets with screenshots from your specific application
2. Adjust the confidence threshold if detection is too strict or too lenient
3. Modify the grid step size based on the density of interactive elements
4. Adjust the timing delays to match the responsiveness of your application

## Troubleshooting

- **Elements not being detected**: Try lowering the confidence threshold or updating the image assets
- **Clicks happening too fast/slow**: Adjust the delay settings in the configuration
- **High CPU usage**: Increase the loop delay times
- **Script crashes**: Check the logs and error screenshots for details

## Safety Notes

- The script includes PyAutoGUI's failsafe feature - move your mouse to the upper-left corner of the screen to abort
- Be cautious when using automation tools with sensitive applications
- Always test in a safe environment before using with important presentations