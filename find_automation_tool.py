import os
import re

def search_for_automation_tool():
    """Search for Python files that might contain the desktop automation tool."""
    
    # Keywords that would likely appear in the automation tool
    automation_keywords = [
        "Object-Step-Sequence",
        "Region Objects",
        "Image Objects", 
        "Pixel Objects",
        "Step Creator",
        "Object Creation",
        "pyautogui",
        "screenshot",
        "click",
        "keyboard",
        "mouse",
        "automation",
        "macro",
        "sequence",
        "tkinter",
        "GUI",
        "Main Menu"
    ]
    
    # Common directories to search
    search_dirs = [
        "C:\\Users\\<USER>\\Documents",
        "C:\\Users\\<USER>\\Desktop",
        "C:\\Users\\<USER>\\OneDrive\\Documents",
        "C:\\Users\\<USER>\\Downloads",
        "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub"
    ]
    
    found_files = []
    
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            print(f"Searching in: {search_dir}")
            for root, dirs, files in os.walk(search_dir):
                # Skip certain directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
                
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                
                            # Count keyword matches
                            matches = 0
                            matched_keywords = []
                            for keyword in automation_keywords:
                                if keyword.lower() in content.lower():
                                    matches += 1
                                    matched_keywords.append(keyword)
                            
                            # If we find multiple keywords, it's likely our automation tool
                            if matches >= 3:
                                found_files.append({
                                    'path': file_path,
                                    'matches': matches,
                                    'keywords': matched_keywords,
                                    'size': len(content)
                                })
                                
                        except Exception as e:
                            print(f"Error reading {file_path}: {e}")
    
    # Sort by number of matches (most likely candidates first)
    found_files.sort(key=lambda x: x['matches'], reverse=True)
    
    print(f"\nFound {len(found_files)} potential automation tool files:")
    print("=" * 60)
    
    for i, file_info in enumerate(found_files[:10]):  # Show top 10
        print(f"{i+1}. {file_info['path']}")
        print(f"   Matches: {file_info['matches']} keywords")
        print(f"   Size: {file_info['size']} characters")
        print(f"   Keywords found: {', '.join(file_info['keywords'][:5])}")
        print()
    
    return found_files

if __name__ == "__main__":
    search_for_automation_tool()
